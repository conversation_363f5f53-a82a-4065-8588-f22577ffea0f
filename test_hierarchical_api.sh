#!/bin/bash

# 测试层次化API功能的脚本

echo "=== 测试层次化上传和搜索功能 ==="

# 获取token（假设用户已登录）
TOKEN=$(curl -s -X POST "http://localhost:8000/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"admin123"}' | jq -r '.access_token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo "❌ 无法获取认证token，请检查登录信息"
  exit 1
fi

echo "✅ 获取到认证token: ${TOKEN:0:20}..."

# 1. 测试层次化上传
echo ""
echo "=== 1. 测试层次化文档上传 ==="

# 创建测试文档
cat > test_hierarchical_doc.txt << 'EOF'
这是一个测试文档，用于验证层次化上传功能。

第一段：系统介绍
本系统是一个基于RAG（Retrieval-Augmented Generation）技术的智能问答系统。它能够理解用户的问题，从知识库中检索相关信息，并生成准确的答案。

第二段：技术架构
系统采用了层次化的文档处理架构，将文档分为父段落和子块两个层次。这种设计既保证了检索的精确性，又提供了丰富的上下文信息。

第三段：功能特点
- 支持多种文档格式：PDF、TXT、Markdown
- 智能文档分割：自动识别段落和句子边界
- 层次化检索：结合精确匹配和上下文信息
- 实时问答：快速响应用户查询

第四段：使用场景
本系统适用于企业知识管理、客户服务、教育培训等多个场景，能够显著提升信息检索和知识共享的效率。
EOF

echo "创建测试文档: test_hierarchical_doc.txt"

# 上传文档（层次化模式）
UPLOAD_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/v1/rag/documents/upload-hierarchical" \
  -H "Authorization: Bearer $TOKEN" \
  -F "file=@test_hierarchical_doc.txt" \
  -F "parent_mode=paragraph" \
  -F "parent_chunk_size=500" \
  -F "parent_chunk_overlap=50" \
  -F "child_chunk_size=200" \
  -F "child_chunk_overlap=20" \
  -F "parent_separators=\\n\\n,\\n,。,. " \
  -F "child_separators=\\n,。,!,?" \
  -F "index_child_chunks_only=true" \
  -F "enable_parent_context=true" \
  -F "preview_only=false")

echo "上传响应:"
echo "$UPLOAD_RESPONSE" | jq .

# 检查上传是否成功
UPLOAD_SUCCESS=$(echo "$UPLOAD_RESPONSE" | jq -r '.success')
if [ "$UPLOAD_SUCCESS" = "true" ]; then
  echo "✅ 层次化文档上传成功"
  DOC_ID=$(echo "$UPLOAD_RESPONSE" | jq -r '.doc_id')
  echo "文档ID: $DOC_ID"
else
  echo "❌ 层次化文档上传失败"
  echo "$UPLOAD_RESPONSE" | jq -r '.message'
fi

# 等待文档处理完成
echo ""
echo "等待3秒让文档处理完成..."
sleep 3

# 2. 测试层次化搜索
echo ""
echo "=== 2. 测试层次化文档搜索 ==="

SEARCH_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/v1/rag/documents/search-hierarchical" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "技术架构和功能特点",
    "top_k": 3,
    "search_all": true,
    "include_parent": false,
    "collection_id": null
  }' \
  --data-urlencode 'score_threshold=0.0' \
  --data-urlencode 'enable_parent_context=true')

echo "搜索响应:"
echo "$SEARCH_RESPONSE" | jq .

# 检查搜索结果
SEARCH_SUCCESS=$(echo "$SEARCH_RESPONSE" | jq -r '.success')
RESULTS_COUNT=$(echo "$SEARCH_RESPONSE" | jq -r '.results | length')

if [ "$SEARCH_SUCCESS" = "true" ] && [ "$RESULTS_COUNT" -gt 0 ]; then
  echo "✅ 层次化搜索成功，找到 $RESULTS_COUNT 个结果"
  
  # 显示搜索结果摘要
  echo ""
  echo "搜索结果摘要:"
  echo "$SEARCH_RESPONSE" | jq -r '.results[] | "- 内容: \(.content[:50])... (分数: \(.score))"'
else
  echo "❌ 层次化搜索失败或无结果"
  echo "搜索成功: $SEARCH_SUCCESS, 结果数量: $RESULTS_COUNT"
fi

# 3. 测试聊天功能（使用层次化搜索）
echo ""
echo "=== 3. 测试RAG聊天功能 ==="

CHAT_RESPONSE=$(curl -s -X POST "http://localhost:8000/api/v1/rag/chat" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "请介绍一下系统的技术架构",
    "enable_rag": true,
    "top_k": 3,
    "conversation_id": ""
  }')

echo "聊天响应:"
echo "$CHAT_RESPONSE" | jq .

CHAT_SUCCESS=$(echo "$CHAT_RESPONSE" | jq -r '.success')
if [ "$CHAT_SUCCESS" = "true" ]; then
  echo "✅ RAG聊天功能正常"
  RESPONSE_TEXT=$(echo "$CHAT_RESPONSE" | jq -r '.response')
  echo "AI回复: ${RESPONSE_TEXT:0:100}..."
else
  echo "❌ RAG聊天功能异常"
  echo "$CHAT_RESPONSE" | jq -r '.message'
fi

# 清理测试文件
rm -f test_hierarchical_doc.txt

echo ""
echo "=== 测试完成 ==="